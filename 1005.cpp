#include<bits/stdc++.h>
using namespace std;

const int N = 20;

struct node
{
    int xx, yy, id;
}a[N];

int T, n, m, k;
char s[N][N];

void modify(int x, int y, int d)
{
    char ch = '0' + d;
    if(x >= 5)
        s[x - 4][y] = ch;
    if(x >= 4)
    {
        for(int i = -1; i <= 1; i ++)
        {
            if(y + i > 0 && y + i <= m)
                s[x - 3][y + i] = ch;
        }
    }
    if(x >= 3)
    {
        for(int i = -2; i <= 2; i ++)
        {
            if(y + i > 0 && y + i <= m)
                s[x - 2][y + i] = ch;
        }
    }
    if(x >= 2)
        s[x - 1][y] = ch;
    if(x >= 1)
        s[x][y] = ch;
}

bool cmp(node x, node y)
{
    return x.xx < y.xx;
}

int main()
{
    ios::sync_with_stdio(0);
    cin.tie(0), cout.tie(0);
    cin >> T;
    while(T --)
    {
        cin >> n >> m >> k;
        for(int i = 1; i <= n; i ++)
        {
            for(int j = 1; j <= m; j ++)
                s[i][j] = '.';
        }

        int x, y;
        for(int i = 1; i <= k; i ++)
        {
            cin >> a[i].xx >> a[i].yy;
            a[i].id = i;
        }

        sort(a + 1, a + k + 1, cmp);

        for(int i = 1; i <= k; i ++)
        {
            modify(a[i].xx, a[i].yy, a[i].id);
        }

        for(int i = 1; i <= n; i ++)
        {
            for(int j = 1; j <= m; j ++)
               cout << s[i][j];
            cout << "\n";
        }
    }
    return 0;
}