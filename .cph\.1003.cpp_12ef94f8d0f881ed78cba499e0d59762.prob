{"name": "Local: 1003", "url": "d:\\橙冰\\信息学\\模拟赛\\2025 暑期训练\\0808 杭电7\\1003.cpp", "tests": [{"id": 1754756409850, "input": "3\n2 1\n3 0\n2 0\n2 1\n3 3\n2 2\n4 2\n1 1\n1 0\n2 0\n1 1", "output": "0/1\n1/1\n1/2"}, {"id": 1754759160136, "input": "2\n5 3\n7 5\n8 3\n6 6\n8 2\n9 1\n8 3\n5 5\n7 3\n89 55\n90 45\n78 77\n7 1\n88 80\n4 3\n5 2", "output": "4/7\n3/4\n"}], "interactive": false, "memoryLimit": 1024, "timeLimit": 3000, "srcPath": "d:\\橙冰\\信息学\\模拟赛\\2025 暑期训练\\0808 杭电7\\1003.cpp", "group": "local", "local": true}