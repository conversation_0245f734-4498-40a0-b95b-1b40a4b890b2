#include<bits/stdc++.h>
using namespace std;

typedef long long ll;

int T;
ll K, N, A, B, C, D;

ll gcd(ll x, ll y)
{
    if(y == 0)
        return x;
    else
        return gcd(y, x % y);
}

bool comp(ll x, ll y, ll xx, ll yy)
{
    if(x * yy >= y * xx)
        return true;
    else
        return false;
}

int main()
{
    ios::sync_with_stdio(0);
    cin.tie(0), cout.tie(0);
    cin >> T;
    while(T --)
    {
        cin >> K >> N >> A >> B >> C >> D;

        ll ansx, ansy;
        ll t;
        t = (K*D)/(C*N)+1;
        ansx = C*t;
        ansy = D;
        //cout << ansx << "/" << ansy << "\n";

        ll Lx, Ly;
        Ly = t * N;
        Lx = K;
        //cout << Lx << "/" << Ly << "\n";

        if(comp(Lx, Ly, A, B)) // L include
        {
            ll resx, resy;
            resx = Lx*((K*Ly)/(Lx*N)+1);
            resy = Ly;
            //cout << resx << "/" << resy << "\n";

            if(comp(resx, resy, ansx, ansy))
            {
                //cout << "* ";
                ll g = gcd(resx, resy);
                //cout << g << endl;
                cout << resx/g << '/' << resy/g << endl;
            }
            else
            {
                //cout << "** ";
                ll g = gcd(ansx, ansy);
                cout << ansx/g << '/' << ansy/g << endl;
            }
        }
        else
        {
            //cout << "** ";
            ll g = gcd(ansx, ansy);
            cout << ansx/g << '/' << ansy/g << endl;
        }
    }
    return 0;
}