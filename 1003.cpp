#include<bits/stdc++.h>
using namespace std;

const int N = 2e5 + 5;
typedef long long ll;

int T, n, k;
ll a[N], b[N], c[N], q[N], lim;

bool check_leq() 
{
    if(lim > 0)
        return false;
    q[1] = 0;
    int len = 1;
    int l, r, mid;
    for(int i = 1; i <= n; i++) 
    {
        if(c[i] < lim || c[i] > 0)
            continue;

        l = 1, r = len;
        while(l < r) 
        {
            //cout << l <<'!' << r << "\n";
            mid = (l + r + 1) / 2;
            if(q[mid] >= c[i])
                l = mid;
            else
                r = mid - 1;
        }
        q[l + 1] = c[i];
        len = max(len, l + 1);
    }

    // for(int i = 1; i <= len; i++)
    //     cout << q[i] << " ";
    // cout << "\n";
    return (len > k);
}

bool check_le() 
{
    if(lim >= 0)
        return false;
    q[1] = 0;
    int len = 1;
    int l, r, mid;
    for(int i = 1; i <= n; i++) 
    {
        if(c[i] < lim || c[i] >= 0)
            continue;

        l = 1, r = len;
        while(l < r) 
        {
            //cout << l <<'&' << r << "\n";
            mid = (l + r + 1) / 2;
            if(q[mid] > c[i])
                l = mid;
            else
                r = mid - 1;
        }
        q[l + 1] = c[i];
        len = max(len, l + 1);
    }
    return (len > k);
}

int check(ll mida, ll midb) 
{
    for(int i = 1; i <= n; i++) 
        c[i] = b[i] * midb - a[i] * mida;
        
    lim = c[n];
    if(check_leq())
    {
        if(!check_le())
            return 0;
        return 1;
    }
    return -1;
}

int main() 
{
    ios::sync_with_stdio(0);
    cin.tie(0), cout.tie(0);
    cin >> T;
    while (T--) 
    {
        cin >> n >> k;
        for (int i = 1; i <= n; i++) 
        {
            cin >> a[i] >> b[i];
            a[i] += a[i - 1];
            b[i] += b[i - 1];
        }   

        if(b[n] == 0)
        {
            cout << "0/1\n";
            continue;
        }
        if(a[n] == b[n])
        {
            cout << "1/1\n";
            continue;
        }
        
        ll La = 0, Lb = 1, Ra = 1, Rb = 1;
        ll mida, midb;
        while(1)
        {
            //cout << cnt << "\n";
            mida = La + Ra;
            midb = Lb + Rb;
            int now = check(mida, midb);

            //cout << mida << "/" << midb << "\n";
            if (now == 0) 
            {
                cout << mida << "/" << midb << "\n";
                break;
            } 

            ll l = 1, r, mid;
            if(now < 0)
            {
                while(1)
                {
                    r = (l << 1);
                    if(check(La+Ra*r, Lb+Rb*r) < 0)
                        l = r;
                    else 
                        break;
                }
                //l ++; 
                r --;
                while(l < r)
                {
                    ll mid = (l + r + 1) >> 1;
                    if(check(La+Ra*mid, Lb+Rb*mid) < 0)
                        l = mid;
                    else 
                        r = mid - 1;
                }
                La += Ra * l;
                Lb += Rb * l;
            }
            else
            {
                while(1)
                {
                    r = (l << 1);
                    if(check(Ra+La*r, Rb+Lb*r) > 0)
                        l = r;
                    else 
                        break;
                }
                //l ++; 
                r --;
                while(l < r)
                {
                    ll mid = (l + r + 1) >> 1;
                    if(check(Ra+La*mid, Rb+Lb*mid) > 0)
                        l = mid;
                    else 
                        r = mid - 1;
                }
                Ra += La * l;
                Rb += Lb * l;
            }
        }
    }
    return 0;
}