{"name": "Local: 1005", "url": "d:\\橙冰\\信息学\\模拟赛\\2025 暑期训练\\0808 杭电7\\1005.cpp", "tests": [{"id": 1754626064228, "input": "2\n7 7 3\n6 4\n4 5\n3 1\n3 4 2\n1 4\n3 2", "output": "333111.\n3.12111\n3.222..\n.22222.\n...2...\n...2...\n.......\n1111\n.1..\n.1.."}, {"id": 1754626550079, "input": "1 \n1 1 1\n1 1", "output": ""}], "interactive": false, "memoryLimit": 1024, "timeLimit": 3000, "srcPath": "d:\\橙冰\\信息学\\模拟赛\\2025 暑期训练\\0808 杭电7\\1005.cpp", "group": "local", "local": true}