#include<bits/stdc++.h>
using namespace std;

typedef long long ll;
const int N = 1e6 + 5;
typedef pair<ll, ll> PII;

priority_queue<PII, vector<PII>, greater<PII> > heap;

int T, n;
ll x[N], y[N], z[N];

int main()
{
    ios::sync_with_stdio(0);
    cin.tie(0), cout.tie(0);
    cin >> T;
    while(T --)
    {
        cin >> n;
        for(int i = 1; i <= n; i ++)
            cin >> x[i] >> y[i] >> z[i];

        sort(x + 1, x + n + 1);
        sort(y + 1, y + n + 1);
        sort(z + 1, z + n + 1);

        while(heap.size())
            heap.pop();

        ll ans = 0;
        for(int i = 1, j = n; i <= j; i ++, j --)
            ans += x[j] - x[i];
        for(int i = 1, j = n; i <= j; i ++, j --)
            ans += y[j] - y[i];
        for(int i = 1, j = n; i <= j; i ++, j --)
            ans += z[j] - z[i];
        
        ll dis = abs(x[n/2+1] + y[n/2+1] + z[n/2+1]);
        ll ch = 0;

        if(x[n/2+1] + y[n/2+1] + z[n/2+1] == 0)
        {
            cout << ans / 2ll << "\n";
            continue;
        }
        else if(x[n/2+1] + y[n/2+1] + z[n/2+1] < 0)
            ch ++;
        else   
            ch --;
        
        ll cnt = 0;
        if(n & 1)
            cnt ++;
        if(n % 2 == 0 && ch > 0)
            cnt = 2;
        ll now = x[n/2+1], id = n/2+1+ch;
        for(int i = id; i > 0 && i <= n; i += ch)
        {
            if(now != x[i])
                heap.push({cnt, abs(now - x[i])});
            now = x[i];
            cnt += 2;
        }
        heap.push({cnt, 1e18});

        cnt = 0;
        if(n & 1)
            cnt ++;
        if(n % 2 == 0 && ch > 0)
            cnt = 2;
        now = y[n/2+1], id = n/2+1+ch;
        for(int i = id; i > 0 && i <= n; i += ch)
        {
            if(now != y[i])
                heap.push({cnt, abs(now - y[i])});
            now = y[i];
            cnt += 2;
        }
        heap.push({cnt, 1e18});

        cnt = 0;
        if(n & 1)
            cnt ++;
        if(n % 2 == 0 && ch > 0)
            cnt = 2;
        now = z[n/2+1], id = n/2+1+ch;
        for(int i = id; i > 0 && i <= n; i += ch)
        {
            if(now != z[i])
                heap.push({cnt, abs(now - z[i])});
            now = z[i];
            cnt += 2;
        }
        heap.push({cnt, 1e18});

        //cout << dis << "\n";

        while(dis > 0 && heap.size())
        {
            PII t = heap.top();
            heap.pop();
            if(t.second >= dis)
            {
                ans += dis * t.first;
                break;
            }
            dis -= t.second;
            ans += t.first * t.second;
        }
        cout << ans / 2ll << "\n";
    }
    return 0;
}